import tkinter as tk
from PIL import Image, ImageTk
import pygame
import threading
import time
import os

# 👻 Load scream
pygame.mixer.init()
pygame.mixer.music.load("scream.mp3")

# 🧠 Type scary text
def type_scare_text(label):
    text = "He is watching you... Don't turn around."
    typed = ""
    for char in text:
        typed += char
        label.config(text=typed)
        time.sleep(0.1)

# 🔊 Scream and image display
def launch_jumpscare():
    root = tk.Tk()
    root.attributes('-fullscreen', True)
    root.configure(bg='black')
    root.overrideredirect(1)  # no title bar

    # 📷 Load scary image
    img = Image.open("scary.png")
    img = img.resize((root.winfo_screenwidth(), root.winfo_screenheight()))
    photo = ImageTk.PhotoImage(img)
    label_img = tk.Label(root, image=photo)
    label_img.pack()

    # 👹 Start scream
    pygame.mixer.music.play()

    # 🧾 Text type effect
    label_text = tk.Label(root, text="", fg="red", bg="black", font=("Courier", 30))
    label_text.place(relx=0.5, rely=0.9, anchor='s')

    threading.Thread(target=type_scare_text, args=(label_text,), daemon=True).start()

    # ⏳ Auto close after 8 secs
    root.after(8000, lambda: root.destroy())
    root.mainloop()

launch_jumpscare()
